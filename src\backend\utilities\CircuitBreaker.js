import client from "./Redis.config.js";

class CircuitBreaker {
  constructor(name, options = {}) {
    this.name = name;
    this.failureThreshold = options.failureThreshold || 5;
    this.resetTimeout = options.resetTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 300000; // 5 minutes
  }

  async getState() {
    try {
      const state = await client.hgetall(`circuit_breaker:${this.name}`);
      return {
        failures: parseInt(state.failures) || 0,
        lastFailureTime: parseInt(state.lastFailureTime) || 0,
        state: state.state || 'CLOSED'
      };
    } catch (error) {
      console.error(`Circuit breaker state error for ${this.name}:`, error);
      return { failures: 0, lastFailureTime: 0, state: 'CLOSED' };
    }
  }

  async recordSuccess() {
    try {
      await client.hset(`circuit_breaker:${this.name}`, {
        failures: 0,
        state: 'CLOSED'
      });
    } catch (error) {
      console.error(`Circuit breaker success record error for ${this.name}:`, error);
    }
  }

  async recordFailure() {
    try {
      const now = Date.now();
      const state = await this.getState();
      const newFailures = state.failures + 1;
      
      const newState = newFailures >= this.failureThreshold ? 'OPEN' : 'CLOSED';
      
      await client.hset(`circuit_breaker:${this.name}`, {
        failures: newFailures,
        lastFailureTime: now,
        state: newState
      });

      if (newState === 'OPEN') {
        console.warn(`🔴 Circuit breaker OPENED for ${this.name} after ${newFailures} failures`);
      }
    } catch (error) {
      console.error(`Circuit breaker failure record error for ${this.name}:`, error);
    }
  }

  async canExecute() {
    const state = await this.getState();
    const now = Date.now();

    if (state.state === 'CLOSED') {
      return true;
    }

    if (state.state === 'OPEN') {
      // Check if we should try again (half-open state)
      if (now - state.lastFailureTime > this.resetTimeout) {
        await client.hset(`circuit_breaker:${this.name}`, 'state', 'HALF_OPEN');
        console.info(`🟡 Circuit breaker HALF-OPEN for ${this.name}`);
        return true;
      }
      return false;
    }

    // HALF_OPEN state - allow one request
    return true;
  }

  async execute(fn, fallback = null) {
    const canExecute = await this.canExecute();
    
    if (!canExecute) {
      console.warn(`🔴 Circuit breaker BLOCKED execution for ${this.name}`);
      if (fallback) {
        return fallback();
      }
      throw new Error(`Service ${this.name} is currently unavailable`);
    }

    try {
      const result = await fn();
      await this.recordSuccess();
      return result;
    } catch (error) {
      await this.recordFailure();
      
      if (fallback) {
        console.warn(`🟡 Circuit breaker fallback executed for ${this.name}:`, error.message);
        return fallback();
      }
      
      throw error;
    }
  }
}

// Create circuit breakers for external services
export const emailCircuitBreaker = new CircuitBreaker('email_service', {
  failureThreshold: 3,
  resetTimeout: 120000, // 2 minutes
});

export const aiScoringCircuitBreaker = new CircuitBreaker('ai_scoring', {
  failureThreshold: 5,
  resetTimeout: 300000, // 5 minutes
});

export default CircuitBreaker;
