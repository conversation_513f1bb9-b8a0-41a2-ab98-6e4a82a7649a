{"name": "examinator", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "validator": "^13.15.15"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "sass": "^1.69.5", "tailwindcss": "^3.3.5", "vite": "^5.0.0"}}