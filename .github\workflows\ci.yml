name: Examinator CI

on:
  push:
    branches: [main, dev, staging]
    paths:
      - "src/backend/**"
      - "src/frontend/**"
      - ".github/workflows/ci.yml"
  pull_request:
    branches: [dev]
    paths:
      - "src/backend/**"
      - "src/frontend/**"
      - ".github/workflows/ci.yml"

jobs:
  paths-filter:
    runs-on: ubuntu-latest
    outputs:
      backend_changed: ${{ steps.filter.outputs.backend }}
      frontend_changed: ${{ steps.filter.outputs.frontend }}
    steps:
      - uses: actions/checkout@v3
      - name: Filter paths
        id: filter
        uses: dorny/paths-filter@v2
        with:
          filters: |
            backend:
              - 'src/backend/**'
            frontend:
              - 'src/frontend/**'
  build-backend:
    name: Build Backend
    needs: paths-filter
    if: needs.paths-filter.outputs.backend_changed == 'true'
    runs-on: ubuntu-latest
    env:
      if: github.ref == 'refs/heads/dev'
      NODE_ENV: development
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      JWT_SECRET: ${{ secrets.JWT_SECRET }}
      REFRESH_TOKEN: ${{ secrets.REFRESH_TOKEN }}
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
      BACKEND_RENDER_DEPLOY_HOOK_URL: ${{ secrets.BACKEND_RENDER_DEPLOY_HOOK_URL }}
      VITE_BASE_URL: ${{ secrets.VITE_BASE_URL }}
      TOGETHERAI_API_KEY: ${{ secrets.TOGETHERAI_API_KEY }}
      #NODE_OPTIONS: --max_old_space_size=4096
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install dependencies
        working-directory: src/backend
        run: npm install

      - name: Lint code
        working-directory: src/backend
        run: npm run lint

      - name: Generate Prisma client
        working-directory: src/backend
        run: npx prisma generate

      # - name: Run tests
      #   working-directory: src/backend
      #   run: npm run test

      # - name: Run backend tests with retry
      #   uses: cirrus-actions/retry@v2
      #   with:
      #     max_attempts: 3
      #     retry_interval: 10
      #     command: |
      #       npm run test

      # - name: Run backend tests with retry
      #   working-directory: src/backend
      #   run: |
      #     n=0
      #     until [ $n -ge 3 ]
      #     do
      #       npm run test && break
      #       n=$((n+1))
      #       echo "Test failed. Retrying $n/3..."
      #       sleep 10
      #     done

      #     if [ $n -eq 3 ]; then
      #       echo "Tests failed after 3 attempts."
      #       exit 1
      #     fi

      - name: Format code
        working-directory: src/backend
        run: npm run prettier

      - name: Check code formatting
        working-directory: src/backend
        run: npm run prettier:check

      # - name: Login to Docker Hub
      #   run: echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Build Docker image
        working-directory: src/backend
        run: docker build -t fortunateomonuwa/examinator-backend:${{github.ref_name}}  .
      - name: Push Docker image
        run: docker push fortunateomonuwa/examinator-backend:${{github.ref_name}}
      - name: Run container
        run: |
          docker run -d \
            -e DATABASE_URL=${{ secrets.DATABASE_URL }} \
            -e JWT_SECRET=${{ secrets.JWT_SECRET }} \
            -e REFRESH_TOKEN=${{ secrets.REFRESH_TOKEN }} \
            -e TOGETHERAI_API_KEY=${{ secrets.TOGETHERAI_API_KEY }} \
            -p 5001:5001 \
            fortunateomonuwa/examinator-backend:${{github.ref_name}}

      - name: Wait for app and show container status
        run: |
          sleep 5
          docker ps
          docker logs $(docker ps -l -q)

      - name: Run Health Check For Container With localhost
        run: |
          curl --fail http://localhost:5001/api/exam/public

  deploy-to-render:
    needs: [paths-filter, build-backend]
    if: needs.paths-filter.outputs.backend_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Render
        if: github.ref == 'refs/heads/dev'
        env:
          NODE_ENV: development

        run: curl -X POST ${{ secrets.BACKEND_RENDER_DEPLOY_HOOK_URL }}

      - name: Run Health Check For Container
        if: github.ref == 'refs/heads/dev'
        env:
          NODE_ENV: development
        run: |
          curl --fail ${{ secrets.VITE_BASE_URL }}/api/exam/public
  # deploy-backend:
  #   needs: build-backend
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Setup Kubectl
  #       uses: azure/setup-kubectl@v3
  #       with:
  #         version: "latest"

  #     - name: Deploy to Kubernetes (Staging)
  #       if: github.ref == 'refs/heads/staging'
  #       env:
  #         KUBECONFIG: ${{ secrets.KUBECONFIG_STAGING }}
  #       run: |
  #         kubectl --kubeconfig=$KUBECONFIG set image deployment/examinator-backend examinator-backend=${{secrets.DOCKER_USERNAME}}/examinator-backend:${{github.ref_name}}
  #         kubectl --kubeconfig=$KUBECONFIG rollout status deployment/examinator-backend

  #     - name: Deploy to Kubernetes (PROD)
  #       if: github.ref == 'refs/heads/main'
  #       env:
  #         KUBECONFIG: ${{ secrets.KUBECONFIG_STAGING }}
  #       run: |
  #         kubectl --kubeconfig=$KUBECONFIG set image deployment/examinator-backend examinator-backend=${{secrets.DOCKER_USERNAME}}/examinator-backend:${{github.ref_name}}
  #         kubectl --kubeconfig=$KUBECONFIG rollout status deployment/examinator-backend

  #     # - name: Deploy to Render via Webhook
  #     #   if: github.ref == 'refs/heads/dev'
  #     #   run: curl -X POST ${{ secrets.RENDER_DEPLOY_HOOK_URL }}

  #     ## Running health check by first fetching
  #     - name: Fetch Service IP
  #       id: get_ip
  #       run: |
  #         echo "Fetching external IP..."
  #         IP=$(kubectl get svc examinator-backend -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  #         echo "IP=$IP"
  #         echo "::set-output name=ip::$IP"

  #     # - name: Fetch Service IP
  #     #   id: get_ip
  #     #   run: |
  #     #       echo "Fetching external IP..."
  #     #       IP=$(kubectl get svc examinator-backend -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
  #     #       if [ -z "$IP"]; then
  #     #         echo "Waiting for IP..."
  #     #         sleep 5
  #     #         IP=$(kubectl get ingress examinator-backend -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
  #     #       fi
  #     #       echo "IP=$IP"
  #     #       echo "::set-output name=ip::$IP"

  #     - name: Run Health Check for Cluster
  #       if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'
  #       run: |
  #         echo "Checking health of deployed app..."
  #         curl --retry 5 --retry-connrefused --fail http://${{ steps.get_ip.outputs.ip }}:80/api/exam/public || echo "Cluster health check failed"

  build-frontend:
    name: Build Frontend
    needs: paths-filter
    if: needs.paths-filter.outputs.frontend_changed == 'true'

    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install dependencies
        working-directory: src/frontend
        run: pnpm install

      - name: Build Frontend (production)
        if: github.ref == 'refs/heads/main'
        working-directory: src/frontend
        env:
          NODE_ENV: production
          VITE_BACKEND_URL: ${{ secrets.VITE_BASE_URL }}
        run: pnpm build
      - name: Build Frontend (development)
        if: github.ref == 'refs/heads/dev'
        working-directory: src/frontend
        env:
          NODE_ENV: development
          VITE_BACKEND_URL: ${{ secrets.VITE_BASE_URL }}
        run: pnpm build
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Build Docker File for Frontend (production)
        if: github.ref == 'refs/heads/main'
        working-directory: src/frontend
        run: docker build -t fortunaeomonuwa/examinator-frontend:${{github.ref_name}}  .

      - name: Build Docker File for Frontend (development)
        if: github.ref == 'refs/heads/dev'
        run: docker build -f src/frontend/development/dockerfile.dev -t fortunateomonuwa/examinator-frontend:${{github.ref_name}}  src/frontend

      - name: Push Docker Image
        run: docker push fortunateomonuwa/examinator-frontend:${{github.ref_name}}
      - name: Run frontend container(dev)
        if: github.ref == 'refs/heads/dev'
        run: |
          docker run -d \
            -p 5173:5173 \
            fortunateomonuwa/examinator-frontend:${{ github.ref_name }}
      - name: Run frontend container(prod)
        if: github.ref == 'refs/heads/main'
        run: |
          docker run -d \
            -p 80:80 \
            fortunateomonuwa/examinator-frontend:${{ github.ref_name }}
      - name: Wait for app and show container status
        run: |
          sleep 5
          docker ps
          docker logs $(docker ps -q)

  deploy-FE-to-render:
    needs: [paths-filter, build-frontend]
    if: needs.paths-filter.outputs.frontend_changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Render
        if: github.ref == 'refs/heads/dev'
        run: curl --fail -X POST ${{ secrets.FRONTEND_RENDER_DEPLOY_HOOK_URL }}

  # deploy-frontend:
  #   needs: build-frontend
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v3

  #     - name: Deploy to Vercel (Dev)
  #       if: github.ref == 'refs/heads/dev'
  #       run: |
  #         echo "Deploy frontend to Vercel"
  #         # e.g. vercel --prod --token ${{ secrets.VERCEL_TOKEN }}

  #     - name: Deploy to Netlify (Prod)
  #       if: github.ref == 'refs/heads/main'
  #       run: |
  #         echo "Deploy frontend to Netlify"
  #         # e.g. netlify deploy --prod --auth ${{ secrets.NETLIFY_AUTH_TOKEN }}
