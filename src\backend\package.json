{"name": "examinator", "version": "1.0.0", "description": "", "main": "main.js", "type": "module", "scripts": {"start": "node main.js", "dev": "nodemon main.js", "test": "vitest run --config ./configs/vitest.config.js", "test:watch": "vitest --config ./configs/vitest.config.js", "test:coverage": "vitest run --coverage ./configs/vitest.config.js", "lint": "eslint . --config ./configs/eslint.config.js", "prettier": "prettier . --write --config ./configs/.prettierrc", "prettier:check": "prettier . --check --config ./configs/.prettierrc"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.3", "nodemailer-sendgrid": "^1.0.3", "openai": "^5.1.1", "redis": "^5.5.6", "together-ai": "^0.16.0", "validator": "^13.15.15"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@eslint/js": "^9.26.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "eslint": "^9.26.0", "globals": "^16.1.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "prisma": "^6.8.1", "supertest": "^7.1.1", "vitest": "^3.1.3"}, "prisma": {"seed": "node prisma/Seed.mjs"}}