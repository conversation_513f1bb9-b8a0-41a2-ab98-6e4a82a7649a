import {
  Login,
  RefreshAccessToken,
  ConfirmUser,
  Logout,
} from "../../imports/ServicesImports.js";
import Response from "../../utilities/Response.js";
import { SendLoginMail } from "../../imports/ServicesImports.js";
const LoginAsync = async (req, res) => {
  const { body } = req;
  const { email, password } = body;

  try {
    const response = await Login({ email: email, password: password });
    if (response.isSuccessful) {
      const { accessToken, user, refreshToken, userId } = response.body;

      const isSecureEnv =
        process.env.NODE_ENV === "production" || process.env.RENDER === "true";

      res.cookie("accessToken", accessToken, {
        httpOnly: true,
        secure: isSecureEnv,
        sameSite: isSecureEnv ? "None" : "Lax",
        maxAge: 7 * 24 * 60 * 60 * 1000,
      });

      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        secure: isSecureEnv,
        sameSite: isSecureEnv ? "None" : "Lax",
        maxAge: 7 * 24 * 60 * 60 * 1000,
      });

      // const sendMail = await SendLoginMail({
      //   to: user.email.toLowerCase(),
      //   name: user.examiner?.name ?? user.student?.name,
      // });
      // if (!sendMail.isSuccessful) {
      //   console.log(sendMail.message);
      //   return res.status(500).json({
      //     response: Response.Unsuccessful({
      //       message: " login failed",
      //       resultCode: 500,
      //     }),
      //   });
      // }
      console.log("Login successfully and response sent");
      return res.status(200).json({
        response: Response.Successful({
          message: "Login successful",
          body: {
            id: user.id, // This is the profile Id... i.e. the user's profile id on the UserProfile Table
            role: user.role,
            email: user.email,
            name: user.examiner?.name ?? user.student?.name,
            userId: userId,
          },
        }),
      });
    } else if (response.error === "unverified") {
      return res.status(401).json({
        response: Response.Unsuccessful({
          message:
            "Your account is not verified. Please verify your account before logging in.",
          resultCode: 401,
          error: "Unverified",
        }),
      });
    } else if (response.error === "Invalid Email or Password") {
      return res.status(401).json({
        response: Response.Unsuccessful({
          message: "Email or password is incorrect",
          resultCode: 401,
          error: "Invalid Email or Password",
        }),
      });
    } else {
      return res
        .status(response.resultCode || 400)
        .json({ response: response });
    }
  } catch (e) {
    return res.status(500).json({
      response: Response.Unsuccessful(),
    });
  }
};

const RefreshAccessTokenAsync = async (req, res) => {
  const { body } = req;
  const { refreshToken } = body;

  try {
    const response = await RefreshAccessToken({ refreshToken: refreshToken });
    if (response.isSuccessful) {
      res.cookie("accessToken", response.body.accessToken, {
        httpOnly: true,
        secure:
          process.env.NODE_ENV === "production" ||
          process.env.NODE_ENV === "development",
        sameSite: "None",
        maxAge: 7 * 24 * 60 * 60 * 1000,
      });

      return res.status(200).json({ response: response });
    } else {
      return res.status(400).json({ response: response });
    }
  } catch (e) {
    return res.status(500).json({
      response: Response.Unsuccessful(),
    });
  }
};

const ConfirmUserAsync = async (req, res) => {
  try {
    const token = req.cookies.accessToken;

    if (!token) {
      return res.status(401).json({
        response: { isSuccessful: false, message: "Not authenticated" },
      });
    }
    const response = await ConfirmUser({ token: token });
    if (!response.isSuccessful) {
      return res.status(401).json({
        response: { isSuccessful: false, message: "Invalid token" },
      });
    }
    const user = response.body;
    res.json({
      response: {
        isSuccessful: true,
        message: "User retrieved",
        body: {
          id: user.id,
          role: user.role,
          email: user.email,
          name: user.examiner?.name ?? user.student?.name,
          userId: user.examiner?.id ?? user.student?.id,
          lastLoginAt: user.authManager?.lastLoginAt,
        },
      },
    });
  } catch (err) {
    return res.status(401).json({
      response: { isSuccessful: false, message: "Invalid token" },
    });
  }
};
const LogoutAsync = (req, res) => {
  const { id } = req.params;
  Logout(id);
  res.clearCookie("accessToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "Strict",
  });

  return res.json({
    response: {
      isSuccessful: true,
      message: "Logged out",
    },
  });
};

export { LoginAsync, RefreshAccessTokenAsync, ConfirmUserAsync, LogoutAsync };
