import { express } from "../../imports/PackageImports.js";
import {
  SendMailAsync,
  ResendVerificationMailAsync,
  SendConfirmationMailAsync,
  SendResetPasswordMailAsync,
  SendExamResultsMailAsync,
  SendExamLinkMailAsync,
} from "../../controllers/IntegrationControllers/Mailer.controller.js";
import {
  AuthenticateToken,
  AuthorizeRole,
} from "../../middleware/AuthMiddleware.js";
import { emailLimiter } from "../../middleware/RateLimitMiddleware.js";
const router = express.Router();

//https://examinator-backend-dev.onrender.com/api/mailer/resend-verification-mail
router.post("/send-mail", emailLimiter, SendMailAsync);
router.post(
  "/resend-verification-mail",
  emailLimiter,
  ResendVerificationMailAsync
);
router.post("/send-verification-mail", emailLimiter, SendConfirmationMailAsync);
router.post("/reset-password", emailLimiter, SendResetPasswordMailAsync);
router.post("/send-exam-results", emailLimiter, SendExamResultsMailAsync);
router.post("/send-exam-link", emailLimiter, SendExamLinkMailAsync);

export { router as MailerRouter };
