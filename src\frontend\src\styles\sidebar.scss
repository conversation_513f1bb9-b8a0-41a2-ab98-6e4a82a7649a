.sidebar {
  .logo {
    font-weight: 700;
    letter-spacing: -0.025em;
    background: linear-gradient(to right, $primary-color, #f84cae, #c10d73);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  a {
    transition: all 0.2s ease;

    &.active {
      font-weight: 600;

      svg {
        color: $primary-color !important;
      }
    }

    &:hover:not(.active) {
      background-color: #f9fafb;
    }
  }
}

// Mobile sidebar overlay styles
.mobile-sidebar-overlay {
  z-index: 40;

  .sidebar-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
  }

  .sidebar-panel {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .close-button {
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
