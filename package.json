{"type": "module", "esm-module-alias": {"@shared": "./shared"}, "imports": {"#shared/*": "./shared/*"}, "scripts": {"dev": "concurrently \"npm run dev:fe\" \"npm run dev:be\"", "dev:fe": "npm run dev --prefix src/frontend", "dev:be": "node --loader esm-module-alias/loader.mjs src/backend/main.js", "start": "concurrently \"npm run start --prefix src/frontend\" \"npm run start --prefix src/backend\""}, "devDependencies": {"concurrently": "^9.1.2", "prettier": "^3.5.3"}, "dependencies": {"esm-module-alias": "^2.2.1", "together-ai": "^0.16.0"}}