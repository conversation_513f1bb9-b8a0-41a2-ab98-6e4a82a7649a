generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Student {
  id           String        @id @default(uuid()) @db.Uuid
  name         String
  profile      UserProfile   @relation(fields: [profileId], references: [id])
  profileId    String        @unique @db.Uuid
  studentExams StudentExam[]
  examAttempts ExamAttempt[] @relation("Student-Attempts")
}

model Examiner {
  id        String      @id @default(uuid()) @db.Uuid
  name      String
  exams     Exam[]      @relation("Exams")
  profile   UserProfile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  profileId String      @unique @db.Uuid
}

model AuthManager {
  id                    String      @id @default(uuid()) @db.Uuid
  userId                String      @unique @db.Uuid
  user                  UserProfile @relation(fields: [userId], references: [id], onDelete: Cascade)
  refreshToken          String?
  refreshTokenExpiresAt DateTime?
  isLoggedIn            Boolean     @default(false)
  lastLoginAt           DateTime?
  lastActivityAt        DateTime?
  loginAttempts         Int         @default(0)
  isLocked              Boolean     @default(false)
  lockedUntil           DateTime?
  createdAt             DateTime    @default(now())
  updatedAt             DateTime    @updatedAt
}

model UserProfile {
  id           String       @id @default(uuid()) @db.Uuid
  email        String       @unique
  passwordHash String
  role         Role
  isVerified   Boolean      @default(false)
  authManager  AuthManager?
  examiner     Examiner?
  student      Student?
  dateCreated  DateTime     @default(now())
  dateUpdated  DateTime     @updatedAt
}

model Exam {
  id               String        @id @default(uuid()) @db.Uuid
  title            String
  description      String
  subject          String        @default("General Knowledge")
  link             String
  creatorId        String        @db.Uuid
  creator          Examiner      @relation("Exams", fields: [creatorId], references: [id], onDelete: NoAction)
  questions        Question[]    @relation("Questions")
  enforceTimeLimit Boolean       @default(false)
  stipulatedTime   Int
  attemptLimit     Int           @default(1)
  dateCreated      DateTime      @default(now())
  dateUpdated      DateTime      @default(now())
  isPublic         Boolean       @default(false)
  studentExams     StudentExam[]
  examAttempts     ExamAttempt[] @relation("Exam")
  level            String        @default("BEGINNER")
}

model StudentExam {
  id          String     @id @default(uuid()) @db.Uuid
  student     Student    @relation(fields: [studentId], references: [id], onDelete: Cascade)
  studentId   String     @db.Uuid
  exam        Exam       @relation(fields: [examId], references: [id], onDelete: Cascade)
  examId      String     @db.Uuid
  status      ExamStatus @default(NOT_STARTED)
  startedAt   DateTime?
  completedAt DateTime?
  timeSpent   Int
  score       String?

  @@unique([studentId, examId])
}

model Question {
  id             String       @id @default(uuid()) @db.Uuid
  text           String
  examId         String       @db.Uuid
  exam           Exam         @relation("Questions", fields: [examId], references: [id], onDelete: Cascade)
  options        Option[]     @relation("Options")
  answers        Answer[]     @relation("Question-Answers")
  required       Boolean
  type           QuestionType
  expectedAnswer String?
  score          Int          @default(1)
}

model Option {
  id         String   @id @default(uuid()) @db.Uuid
  text       String
  questionId String   @db.Uuid
  question   Question @relation("Options", fields: [questionId], references: [id], onDelete: Cascade)
  isCorrect  Boolean
  answers    Answer[]
}

model ExamAttempt {
  id            String    @id @default(uuid()) @db.Uuid
  studentId     String?   @db.Uuid
  student       Student?  @relation("Student-Attempts", fields: [studentId], references: [id])
  examId        String    @db.Uuid
  exam          Exam      @relation("Exam", fields: [examId], references: [id], onDelete: Cascade)
  answers       Answer[]  @relation("Attempt-Answers")
  startTime     DateTime?
  submittedAt   DateTime  @default(now())
  totalScore    Float?
  responderEmal String?
  responderName String?
}

model Answer {
  id         String      @id @default(uuid()) @db.Uuid
  attemptId  String      @db.Uuid
  attempt    ExamAttempt @relation("Attempt-Answers", fields: [attemptId], references: [id], onDelete: Cascade)
  questionId String      @db.Uuid
  question   Question    @relation("Question-Answers", fields: [questionId], references: [id], onDelete: Cascade)
  options    Option[]
  textAnswer String[]
  score      Float?
}

enum Role {
  ADMIN
  STUDENT
  EXAMINER
}

enum ExamStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

enum QuestionType {
  SINGLECHOICE
  MULTICHOICE
  TEXT
}
