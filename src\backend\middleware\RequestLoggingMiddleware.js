import client from "../utilities/Redis.config.js";

// Request logging middleware
export const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const originalSend = res.send;
  
  // Override res.send to capture response
  res.send = function(data) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Log request details
    const logData = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      responseTime,
      statusCode: res.statusCode,
      timestamp: new Date().toISOString(),
      userId: req.user?.profileId || null,
    };

    // Log slow queries (>100ms)
    if (responseTime > 100) {
      console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.originalUrl} - ${responseTime}ms`);
    }

    // Log errors
    if (res.statusCode >= 400) {
      console.error(`❌ ERROR REQUEST: ${req.method} ${req.originalUrl} - Status: ${res.statusCode} - ${responseTime}ms`);
      
      // Store error metrics in Redis
      storeErrorMetrics(req.originalUrl, res.statusCode);
    }

    // Store performance metrics
    storePerformanceMetrics(req.originalUrl, responseTime);

    // Call original send
    originalSend.call(this, data);
  };

  next();
};

// Store error metrics in Redis
const storeErrorMetrics = async (endpoint, statusCode) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const errorKey = `errors:${today}:${endpoint}:${statusCode}`;
    await client.incr(errorKey);
    await client.expire(errorKey, 7 * 24 * 60 * 60); // Keep for 7 days
  } catch (error) {
    console.error("Failed to store error metrics:", error);
  }
};

// Store performance metrics in Redis
const storePerformanceMetrics = async (endpoint, responseTime) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const perfKey = `performance:${today}:${endpoint}`;
    
    // Store response time in a list (keep last 100 requests)
    await client.lpush(perfKey, responseTime);
    await client.ltrim(perfKey, 0, 99); // Keep only last 100 entries
    await client.expire(perfKey, 7 * 24 * 60 * 60); // Keep for 7 days
  } catch (error) {
    console.error("Failed to store performance metrics:", error);
  }
};

// Session cleanup middleware
export const sessionCleanup = async (req, res, next) => {
  try {
    // Clean up expired sessions every hour
    const lastCleanup = await client.get("last_session_cleanup");
    const now = Date.now();
    
    if (!lastCleanup || (now - parseInt(lastCleanup)) > 3600000) { // 1 hour
      // Clean up expired exam sessions
      const examSessionKeys = await client.keys("exam_session:*");
      for (const key of examSessionKeys) {
        const ttl = await client.ttl(key);
        if (ttl === -1) { // No expiration set
          await client.expire(key, 3600); // Set 1 hour expiration
        }
      }
      
      await client.set("last_session_cleanup", now.toString());
    }
  } catch (error) {
    console.error("Session cleanup error:", error);
  }
  
  next();
};

// Concurrent exam session management
export const examSessionManager = async (req, res, next) => {
  if (req.method === 'POST' && req.originalUrl.includes('/exam-attempt/submit')) {
    const { examId, responderEmail } = req.body;
    
    if (examId && responderEmail) {
      const sessionKey = `exam_session:${examId}:${responderEmail.toLowerCase()}`;
      
      try {
        // Check if there's an active session
        const existingSession = await client.get(sessionKey);
        
        if (existingSession) {
          const sessionData = JSON.parse(existingSession);
          const now = Date.now();
          
          // If session is less than 5 minutes old, prevent duplicate submission
          if ((now - sessionData.startTime) < 300000) { // 5 minutes
            return res.status(409).json({
              response: {
                isSuccessful: false,
                message: "Exam submission already in progress. Please wait.",
                resultCode: 409
              }
            });
          }
        }
        
        // Create new session
        await client.setex(sessionKey, 300, JSON.stringify({ // 5 minutes expiration
          startTime: Date.now(),
          examId,
          responderEmail: responderEmail.toLowerCase()
        }));
        
      } catch (error) {
        console.error("Exam session management error:", error);
      }
    }
  }
  
  next();
};
