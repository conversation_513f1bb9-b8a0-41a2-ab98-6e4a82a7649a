import rateLimit from "express-rate-limit";
import client from "../utilities/Redis.config.js";

// Store for rate limiting using Redis
const RedisStore = class {
  constructor(options = {}) {
    this.prefix = options.prefix || "rl:";
    this.client = client;
  }

  async increment(key) {
    const redisKey = this.prefix + key;
    try {
      const current = await this.client.incr(redisKey);
      if (current === 1) {
        await this.client.expire(redisKey, 900); // 15 minutes default
      }
      return { totalHits: current, resetTime: new Date(Date.now() + 900000) };
    } catch (error) {
      console.error("Redis rate limit error:", error);
      // Fallback to memory-based limiting
      return { totalHits: 1, resetTime: new Date(Date.now() + 900000) };
    }
  }

  async decrement(key) {
    const redisKey = this.prefix + key;
    try {
      await this.client.decr(redisKey);
    } catch (error) {
      console.error("Redis rate limit decrement error:", error);
    }
  }

  async resetKey(key) {
    const redisKey = this.prefix + key;
    try {
      await this.client.del(redisKey);
    } catch (error) {
      console.error("Redis rate limit reset error:", error);
    }
  }
};

// Exam submission rate limiting - 1 submission per exam per user
export const examSubmissionLimiter = rateLimit({
  store: new RedisStore({ prefix: "exam_submit:" }),
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 1,
  keyGenerator: (req) => {
    const { examId } = req.body;
    const { responderEmail } = req.body;
    return `${examId}:${responderEmail?.toLowerCase()}`;
  },
  message: {
    error: "You have already submitted this exam. Multiple submissions are not allowed.",
    resultCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Get exam questions rate limiting - 10 requests per minute per IP
export const examQuestionsLimiter = rateLimit({
  store: new RedisStore({ prefix: "exam_questions:" }),
  windowMs: 60 * 1000, // 1 minute
  max: 10,
  keyGenerator: (req) => req.ip,
  message: {
    error: "Too many requests to view exam questions. Please try again later.",
    resultCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Exam creation rate limiting - 10 exams per hour per user
export const examCreationLimiter = rateLimit({
  store: new RedisStore({ prefix: "exam_create:" }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10,
  keyGenerator: (req) => req.user?.profileId || req.ip,
  message: {
    error: "Too many exams created. Please wait before creating more exams.",
    resultCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Email sending rate limiting - 50 emails per hour per user
export const emailLimiter = rateLimit({
  store: new RedisStore({ prefix: "email_send:" }),
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50,
  keyGenerator: (req) => req.user?.profileId || req.ip,
  message: {
    error: "Email sending limit exceeded. Please wait before sending more emails.",
    resultCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// General API rate limiting - 100 requests per 15 minutes per IP
export const generalLimiter = rateLimit({
  store: new RedisStore({ prefix: "general:" }),
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  keyGenerator: (req) => req.ip,
  message: {
    error: "Too many requests from this IP. Please try again later.",
    resultCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
});
